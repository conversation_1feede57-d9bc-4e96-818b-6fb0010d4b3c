import { useBreakpoints } from "@/components/hooks/useBreakpoints"

// Hook version - use this at the component level
function useResponsiveLabel() {
    const bp = useBreakpoints()
    return (short: string, long: string) => bp.small ? long : short
}

// Utility version - use this when you have breakpoint state available
function getResponsiveLabel(isSmallScreen: boolean, short: string, long: string) {
    return isSmallScreen ? long : short
}

// Legacy hook version for backward compatibility (but should be avoided in conditional rendering)
function responsiveLabel(short: string, long: string) {
    const bp = useBreakpoints()
    return bp.small ? long : short
}

export default responsiveLabel
export { useResponsiveLabel, getResponsiveLabel }
